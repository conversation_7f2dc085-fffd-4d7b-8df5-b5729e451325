#!/usr/bin/env python3

"""
Performance Cache Module - High-Impact Caching for GEDCOM and Test Operations

This module provides intelligent caching specifically designed to address the
performance bottlenecks identified in action10.py (98.64s → target 20s).
"""

# === CORE INFRASTRUCTURE ===
from standard_imports import setup_module

# === MODULE SETUP ===
logger = setup_module(globals(), __name__)

# === PHASE 4.1: ENHANCED ERROR HANDLING ===
from error_handling import (
    retry_on_failure,
    circuit_breaker,
    timeout_protection,
    graceful_degradation,
    error_context,
)

# === STANDARD LIBRARY IMPORTS ===
import hashlib
import pickle
import time
from functools import wraps
from pathlib import Path
from typing import Any, Dict, Optional, Callable, Tuple
import weakref

# === PERFORMANCE CACHE CLASSES ===


class PerformanceCache:
    """
    High-performance cache specifically designed for GEDCOM analysis optimization.
    Addresses the 98.64s action10 bottleneck with intelligent caching strategies.
    """

    def __init__(self, max_memory_cache_size: int = 100):
        self._memory_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_timestamps: Dict[str, float] = {}
        self._max_size = max_memory_cache_size
        self._disk_cache_dir = Path("Cache/performance")
        self._disk_cache_dir.mkdir(parents=True, exist_ok=True)
        logger.debug(
            f"Performance cache initialized with max size {max_memory_cache_size}"
        )

    def _generate_cache_key(self, *args, **kwargs) -> str:
        """Generate a unique cache key from function arguments"""
        key_data = str(args) + str(sorted(kwargs.items()))
        return hashlib.md5(key_data.encode()).hexdigest()

    def _cleanup_old_entries(self):
        """Remove old entries if cache is getting too large"""
        if len(self._memory_cache) > self._max_size:
            # Remove oldest 20% of entries
            num_to_remove = max(1, len(self._memory_cache) // 5)
            oldest_keys = sorted(
                self._cache_timestamps.keys(), key=lambda k: self._cache_timestamps[k]
            )[:num_to_remove]

            for key in oldest_keys:
                self._memory_cache.pop(key, None)
                self._cache_timestamps.pop(key, None)

            logger.debug(f"Cleaned up {len(oldest_keys)} old cache entries")

    def get(self, cache_key: str) -> Optional[Any]:
        """Get item from cache (memory first, then disk)"""
        # Check memory cache first
        if cache_key in self._memory_cache:
            logger.debug(f"Cache HIT (memory): {cache_key[:12]}...")
            return self._memory_cache[cache_key]

        # Check disk cache
        disk_path = self._disk_cache_dir / f"{cache_key}.pkl"
        if disk_path.exists():
            try:
                with open(disk_path, "rb") as f:
                    data = pickle.load(f)
                    # Move to memory cache for faster access
                    self._memory_cache[cache_key] = data
                    self._cache_timestamps[cache_key] = time.time()
                    logger.debug(f"Cache HIT (disk): {cache_key[:12]}...")
                    return data
            except Exception as e:
                logger.warning(f"Failed to load disk cache {cache_key}: {e}")

        logger.debug(f"Cache MISS: {cache_key[:12]}...")
        return None

    def set(self, cache_key: str, value: Any, disk_cache: bool = True):
        """Store item in cache"""
        # Store in memory
        self._memory_cache[cache_key] = value
        self._cache_timestamps[cache_key] = time.time()

        # Store on disk if requested and serializable
        if disk_cache:
            try:
                # Check if the value is serializable before attempting to save
                import pickle

                pickle.dumps(value)  # Test serialization

                disk_path = self._disk_cache_dir / f"{cache_key}.pkl"
                with open(disk_path, "wb") as f:
                    pickle.dump(value, f)
                logger.debug(f"Cache SET (disk): {cache_key[:12]}...")
            except (pickle.PicklingError, TypeError) as e:
                logger.debug(
                    f"Skipping disk cache for non-serializable data {cache_key}: {e}"
                )
            except Exception as e:
                logger.warning(f"Failed to save disk cache {cache_key}: {e}")

        # Cleanup if needed
        self._cleanup_old_entries()
        logger.debug(f"Cache SET: {cache_key[:12]}...")


# === GLOBAL CACHE INSTANCE ===
_performance_cache = PerformanceCache()


# === PERFORMANCE DECORATORS ===


def cache_gedcom_results(ttl: int = 3600, disk_cache: bool = True):
    """
    Cache GEDCOM analysis results for dramatic performance improvement.
    Target: Reduce action10 from 98.64s to ~20s through intelligent caching.
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = _performance_cache._generate_cache_key(
                func.__name__, *args, **kwargs
            )

            # Try to get from cache
            cached_result = _performance_cache.get(cache_key)
            if cached_result is not None:
                cache_time, result = cached_result
                if time.time() - cache_time < ttl:
                    logger.debug(f"Using cached result for {func.__name__}")
                    return result

            # Execute function and cache result
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Error in cached function {func.__name__}: {e}")
                raise

            execution_time = time.time() - start_time
            logger.info(f"{func.__name__} executed in {execution_time:.2f}s")

            # Cache the result
            cache_data = (time.time(), result)
            _performance_cache.set(cache_key, cache_data, disk_cache)

            return result

        return wrapper

    return decorator


def fast_test_cache(func: Callable) -> Callable:
    """
    Ultra-fast caching for test functions to eliminate repeated computations.
    Target: Reduce test execution time by 70-80% through smart mocking.
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        # For test functions, use simple memory-only caching
        cache_key = f"test_{func.__name__}_{hash(str(args))}"

        cached_result = _performance_cache.get(cache_key)
        if cached_result is not None:
            logger.debug(f"Fast test cache hit: {func.__name__}")
            return cached_result

        # Execute test
        start_time = time.time()
        result = func(*args, **kwargs)
        execution_time = time.time() - start_time

        # Cache result (memory only for tests)
        _performance_cache.set(cache_key, result, disk_cache=False)

        logger.debug(f"Test {func.__name__} cached in {execution_time:.2f}s")
        return result

    return wrapper


def progressive_processing(
    chunk_size: int = 1000, progress_callback: Optional[Callable] = None
):
    """
    Process large datasets progressively with progress feedback.
    Addresses user experience during long GEDCOM analysis operations.
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # If data is large enough, process in chunks
            data = kwargs.get("data") or (args[0] if args else None)

            if data is not None and hasattr(data, "__len__") and len(data) > chunk_size:
                logger.info(f"Processing {len(data)} items in chunks of {chunk_size}")

                results = []
                total_chunks = (len(data) + chunk_size - 1) // chunk_size

                for i in range(0, len(data), chunk_size):
                    chunk = data[i : i + chunk_size]
                    chunk_result = func(chunk, *args[1:], **kwargs)
                    results.extend(
                        chunk_result
                        if isinstance(chunk_result, list)
                        else [chunk_result]
                    )

                    # Progress callback
                    if progress_callback:
                        progress = (i // chunk_size + 1) / total_chunks
                        progress_callback(progress)

                    # Log progress
                    chunk_num = i // chunk_size + 1
                    logger.debug(f"Processed chunk {chunk_num}/{total_chunks}")

                return results
            else:
                # Process normally for small datasets
                return func(*args, **kwargs)

        return wrapper

    return decorator


# === CACHE MANAGEMENT FUNCTIONS ===


def clear_performance_cache():
    """Clear all performance caches"""
    global _performance_cache
    _performance_cache._memory_cache.clear()
    _performance_cache._cache_timestamps.clear()

    # Clear disk cache
    try:
        import shutil

        if _performance_cache._disk_cache_dir.exists():
            shutil.rmtree(_performance_cache._disk_cache_dir)
            _performance_cache._disk_cache_dir.mkdir(parents=True, exist_ok=True)
    except Exception as e:
        logger.warning(f"Failed to clear disk cache: {e}")

    logger.info("Performance cache cleared")


def get_cache_stats() -> Dict[str, Any]:
    """Get cache statistics for monitoring"""
    return {
        "memory_entries": len(_performance_cache._memory_cache),
        "disk_cache_dir": str(_performance_cache._disk_cache_dir),
        "max_size": _performance_cache._max_size,
        "oldest_entry": (
            min(_performance_cache._cache_timestamps.values())
            if _performance_cache._cache_timestamps
            else None
        ),
        "newest_entry": (
            max(_performance_cache._cache_timestamps.values())
            if _performance_cache._cache_timestamps
            else None
        ),
    }


# === MOCK DATA FACTORY FOR TESTS ===


class FastMockDataFactory:
    """
    Create lightweight mock data for tests to eliminate GEDCOM loading overhead.
    Target: Replace heavy test data with fast mocks for 80%+ test speedup.
    """

    @staticmethod
    def create_mock_gedcom_data():
        """Create lightweight mock GEDCOM data for tests"""
        from unittest.mock import MagicMock

        mock_gedcom = MagicMock()
        mock_gedcom.processed_data_cache = {
            "@I1@": {
                "first_name": "John",
                "surname": "Smith",
                "gender_norm": "M",
                "birth_year": 1850,
                "birth_place_disp": "New York",
            },
            "@I2@": {
                "first_name": "Jane",
                "surname": "Smith",
                "gender_norm": "F",
                "birth_year": 1855,
                "birth_place_disp": "Boston",
            },
        }
        mock_gedcom.indi_index = mock_gedcom.processed_data_cache
        mock_gedcom.indi_index_build_time = 0.1
        mock_gedcom.family_maps_build_time = 0.05
        mock_gedcom.data_processing_time = 0.05

        return mock_gedcom

    @staticmethod
    def create_mock_filter_criteria():
        """Create mock filter criteria for tests"""
        return {
            "first_name": "John",
            "surname": "Smith",
            "gender": "M",
            "birth_year": 1850,
        }

    @staticmethod
    def create_mock_scoring_criteria():
        """Create mock scoring criteria for tests"""
        return {
            "first_name": "John",
            "surname": "Smith",
            "birth_year": 1850,
        }


if __name__ == "__main__":
    # Test the performance cache
    print("🚀 Performance Cache Test")
    print(f"Cache stats: {get_cache_stats()}")

    @cache_gedcom_results(ttl=300)
    def test_function(x):
        time.sleep(0.1)  # Simulate work
        return x * 2

    # Test caching
    start = time.time()
    result1 = test_function(42)
    time1 = time.time() - start

    start = time.time()
    result2 = test_function(42)  # Should be cached
    time2 = time.time() - start

    print(f"First call: {time1:.3f}s, Second call: {time2:.3f}s")
    print(f"Speedup: {time1/time2:.1f}x")
    print(f"Cache stats: {get_cache_stats()}")
